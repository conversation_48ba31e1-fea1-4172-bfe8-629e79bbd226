#!/usr/bin/env python3
"""
System Monitor - Test Suite
Comprehensive testing of all system components
"""

import requests
import json
import time
import sys
import subprocess
import os

def test_server_connection(server_url="http://localhost:5000"):
    """Test if server is accessible"""
    print("🔍 Testing server connection...")
    try:
        response = requests.get(f"{server_url}/api/stats", timeout=5)
        if response.status_code == 200:
            print("✅ Server is accessible")
            return True
        else:
            print(f"❌ Server returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to server: {e}")
        return False

def test_api_endpoints(server_url="http://localhost:5000"):
    """Test all API endpoints"""
    print("\n🔍 Testing API endpoints...")
    
    endpoints = [
        ("/api/stats", "Statistics"),
        ("/api/clients", "Client list"),
        ("/", "Web console")
    ]
    
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"{server_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {description}: OK")
            else:
                print(f"❌ {description}: Status {response.status_code}")
        except Exception as e:
            print(f"❌ {description}: Error - {e}")

def test_client_reporting(server_url="http://localhost:5000"):
    """Test client reporting functionality"""
    print("\n🔍 Testing client reporting...")
    
    # Run client once
    try:
        if os.path.exists(".venv/Scripts/python"):
            python_cmd = ".venv/Scripts/python"
        else:
            python_cmd = "python"
            
        result = subprocess.run([
            python_cmd, "System Monitor Client.py", 
            "--server", server_url, "--once"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Client reporting: OK")
            print(f"   Output: {result.stdout.strip()}")
        else:
            print(f"❌ Client reporting failed: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Client test error: {e}")

def test_database_functionality(server_url="http://localhost:5000"):
    """Test database operations"""
    print("\n🔍 Testing database functionality...")
    
    try:
        # Get clients
        response = requests.get(f"{server_url}/api/clients")
        if response.status_code == 200:
            clients = response.json()
            print(f"✅ Database query: Found {len(clients)} clients")
            
            if clients:
                # Test client details
                client_id = clients[0]['client_id']
                detail_response = requests.get(f"{server_url}/api/client/{client_id}")
                if detail_response.status_code == 200:
                    print("✅ Client details: OK")
                else:
                    print("❌ Client details: Failed")
        else:
            print("❌ Database query failed")
            
    except Exception as e:
        print(f"❌ Database test error: {e}")

def display_system_info(server_url="http://localhost:5000"):
    """Display current system information"""
    print("\n📊 Current System Information:")
    print("=" * 50)
    
    try:
        # Get statistics
        stats_response = requests.get(f"{server_url}/api/stats")
        if stats_response.status_code == 200:
            stats = stats_response.json()
            print(f"Total Clients: {stats['total_clients']}")
            print(f"Active Clients: {stats['active_clients']}")
            print(f"Offline Clients: {stats['offline_clients']}")
            print(f"Average Memory: {stats['average_memory_gb']} GB")
            
        # Get client list
        clients_response = requests.get(f"{server_url}/api/clients")
        if clients_response.status_code == 200:
            clients = clients_response.json()
            print(f"\nRegistered Clients:")
            for client in clients:
                status_icon = "🟢" if client['status'] == 'active' else "🔴"
                print(f"  {status_icon} {client['computer_name']} ({client['client_id']})")
                print(f"     Platform: {client['platform']} {client['platform_version']}")
                print(f"     Last Seen: {client['last_seen']}")
                print()
                
    except Exception as e:
        print(f"❌ Error getting system info: {e}")

def main():
    """Main test function"""
    print("🚀 System Monitor - Comprehensive Test Suite")
    print("=" * 60)
    
    server_url = "http://localhost:5000"
    
    # Test server connection
    if not test_server_connection(server_url):
        print("\n❌ Server is not running. Please start the server first.")
        print("   Run: python \"System Monitor Central Server.py\"")
        sys.exit(1)
    
    # Run all tests
    test_api_endpoints(server_url)
    test_client_reporting(server_url)
    test_database_functionality(server_url)
    
    # Display current information
    display_system_info(server_url)
    
    print("\n🎉 Test Suite Complete!")
    print(f"📱 Web Console: {server_url}")
    print("🔄 For continuous monitoring, run:")
    print(f"   python \"System Monitor Client.py\" --server {server_url}")

if __name__ == "__main__":
    main()

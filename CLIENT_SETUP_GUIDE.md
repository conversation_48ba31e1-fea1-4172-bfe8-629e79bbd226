# 📋 System Monitor - Client Setup Guide

Complete guide for adding and managing clients in your System Monitor infrastructure.

## 🎯 Overview

There are multiple ways to add clients to your System Monitor:
1. **Automated Installation** (Recommended for production)
2. **Manual Installation** (For testing/development)
3. **Remote Deployment** (For enterprise environments)

## 🚀 Method 1: Automated Installation (Recommended)

### Windows Clients

1. **Prepare Installation Package**
   ```cmd
   # Copy these files to target machine:
   - Windows Client Installer.bat
   - System Monitor Client.py
   - Requirements File.txt
   ```

2. **Run Installation**
   ```cmd
   # On target Windows machine (as Administrator):
   cd C:\path\to\installation\files
   "Windows Client Installer.bat"
   ```

3. **Configuration During Installation**
   - Server URL: `http://your-server-ip:5000`
   - Reporting Interval: `60` minutes (recommended)
   - Client ID: Leave blank for hostname or specify custom ID

4. **Verify Installation**
   ```cmd
   # Check service status
   sc query SystemMonitorClient
   
   # View service logs
   # Check Windows Event Viewer > Applications and Services Logs
   ```

### Linux Clients

1. **Prepare Installation Package**
   ```bash
   # Copy these files to target machine:
   - Linux Installation Script.sh
   - System Monitor Client.py
   - Requirements File.txt
   ```

2. **Run Installation**
   ```bash
   # On target Linux machine (as root):
   sudo chmod +x "Linux Installation Script.sh"
   sudo ./Linux\ Installation\ Script.sh
   ```

3. **Configuration During Installation**
   - Server URL: `http://your-server-ip:5000`
   - Reporting Interval: `60` minutes (recommended)
   - Client ID: Leave blank for hostname or specify custom ID

4. **Verify Installation**
   ```bash
   # Check service status
   sudo systemctl status system-monitor-client
   
   # View service logs
   sudo journalctl -u system-monitor-client -f
   ```

## 🛠️ Method 2: Manual Installation (Development/Testing)

### Prerequisites
```bash
# Ensure Python 3.7+ is installed
python --version

# Install required packages
pip install psutil requests flask
```

### Quick Setup
```bash
# 1. Copy client file to target machine
# 2. Test connection
python "System Monitor Client.py" --server http://your-server-ip:5000 --once

# 3. Start continuous monitoring
python "System Monitor Client.py" --server http://your-server-ip:5000 --interval 60
```

## 🌐 Method 3: Remote Deployment (Enterprise)

### Using PowerShell (Windows)
```powershell
# Deploy to multiple Windows machines
$computers = @("PC1", "PC2", "PC3")
$serverUrl = "http://your-server-ip:5000"

foreach ($computer in $computers) {
    # Copy installation files
    Copy-Item -Path ".\installation\*" -Destination "\\$computer\C$\temp\" -Recurse
    
    # Execute remote installation
    Invoke-Command -ComputerName $computer -ScriptBlock {
        cd C:\temp
        & ".\Windows Client Installer.bat"
    }
}
```

### Using Ansible (Linux)
```yaml
# ansible-playbook deploy-clients.yml
---
- hosts: linux_clients
  become: yes
  tasks:
    - name: Copy installation files
      copy:
        src: "{{ item }}"
        dest: /tmp/
      with_items:
        - Linux Installation Script.sh
        - System Monitor Client.py
        - Requirements File.txt
    
    - name: Run installation
      shell: |
        cd /tmp
        chmod +x "Linux Installation Script.sh"
        ./Linux\ Installation\ Script.sh
```

## 🔧 Configuration Options

### Client Configuration Parameters
```bash
python "System Monitor Client.py" [OPTIONS]

Required:
  --server URL          # Server URL (e.g., http://*************:5000)

Optional:
  --client-id ID        # Custom client identifier
  --interval MINUTES    # Reporting interval (default: 60)
  --once               # Run once and exit (for testing)
  --local-only         # Save reports locally only
```

### Server Configuration
Edit `System Monitor Central Server.py`:
```python
# Change server binding
app.run(host='0.0.0.0', port=5000, debug=False)  # Production
app.run(host='127.0.0.1', port=5000, debug=True)  # Development
```

## 🔍 Verification Steps

### 1. Check Client Registration
- Open web console: `http://your-server-ip:5000`
- Verify client appears in the client list
- Check "Last Seen" timestamp is recent

### 2. Test Client Details
- Click "Details" button for the client
- Verify system information is displayed
- Check all data fields are populated

### 3. Monitor Continuous Reporting
- Wait for next reporting interval
- Refresh web console
- Verify "Last Seen" timestamp updates

## 🚨 Troubleshooting

### Common Issues

**Client not appearing in console:**
```bash
# Check network connectivity
ping your-server-ip

# Test manual connection
python "System Monitor Client.py" --server http://your-server-ip:5000 --once

# Check firewall settings
# Windows: Windows Defender Firewall
# Linux: ufw status / iptables -L
```

**Service won't start:**
```bash
# Windows
sc query SystemMonitorClient
sc start SystemMonitorClient

# Linux
sudo systemctl status system-monitor-client
sudo systemctl start system-monitor-client
```

**Permission errors:**
```bash
# Ensure installation ran with proper privileges
# Windows: Run as Administrator
# Linux: Run with sudo
```

### Log Locations

**Windows:**
- Service logs: Windows Event Viewer
- Installation logs: Console output during installation

**Linux:**
- Service logs: `sudo journalctl -u system-monitor-client`
- Installation logs: Console output during installation

## 📊 Monitoring Client Health

### Server-side Monitoring
- Clients automatically marked offline after 2 hours of no contact
- Web console shows real-time status
- API endpoints provide programmatic access

### Client-side Monitoring
```bash
# Check if client is running
# Windows
tasklist | findstr python

# Linux
ps aux | grep python
```

## 🔄 Client Management

### Update Clients
1. Stop the service
2. Replace client files
3. Restart the service

### Remove Clients
```bash
# Windows
sc stop SystemMonitorClient
sc delete SystemMonitorClient

# Linux
sudo systemctl stop system-monitor-client
sudo systemctl disable system-monitor-client
sudo rm /etc/systemd/system/system-monitor-client.service
```

### Bulk Operations
Use the provided PowerShell/Ansible scripts for managing multiple clients simultaneously.

---

## 📞 Support

For additional help:
1. Check server logs for connection attempts
2. Verify network connectivity between client and server
3. Ensure all required ports are open (default: 5000)
4. Review client and server logs for error messages

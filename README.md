# System Monitor - Enterprise System Management Solution

A comprehensive system monitoring platform similar to Microsoft SCCM, designed for centralized management and monitoring of computer systems across your network.

## 🚀 Features

### Central Server
- **Web-based Console** - Modern web interface for system management
- **REST API** - Complete API for programmatic access
- **Real-time Monitoring** - Live system status and statistics
- **SQLite Database** - Persistent storage of system data
- **Client Status Tracking** - Automatic online/offline detection

### Client Agent
- **Comprehensive System Information** - Hardware, software, and performance data
- **Automatic Reporting** - Configurable intervals for data collection
- **Network Resilience** - Handles offline scenarios gracefully
- **Cross-platform** - Windows and Linux support
- **Service Integration** - Runs as system service

### Deployment Tools
- **Automated Installation** - Scripts for Windows and Linux
- **Service Management** - Automatic service creation and management
- **Configuration Management** - Centralized configuration options

## 📋 System Requirements

### Server Requirements
- Python 3.7+
- 2GB RAM minimum
- 10GB disk space
- Network connectivity

### Client Requirements
- Python 3.7+
- 512MB RAM minimum
- Network connectivity to server

## 🛠️ Quick Start

### Option 1: Development/Testing (Current Setup)
```bash
# Windows
start_system.bat

# Linux
./start_system.sh
```

### Option 2: Production Deployment

#### Server Installation
```bash
# 1. Install dependencies
pip install -r "Requirements File.txt"

# 2. Start server
python "System Monitor Central Server.py"

# 3. Access web console
# Open browser to http://localhost:5000
```

#### Client Installation

**Windows:**
```cmd
# Run as Administrator
"Windows Client Installer.bat"
```

**Linux:**
```bash
# Run as root
sudo ./Linux\ Installation\ Script.sh
```

## 🌐 Web Console

Access the management console at: `http://localhost:5000`

### Features:
- **Dashboard** - Overview of all managed systems
- **Client List** - Detailed view of all connected clients
- **System Details** - Comprehensive information per client
- **Statistics** - Aggregate data and trends
- **Real-time Updates** - Auto-refresh every 30 seconds

## 📊 API Endpoints

### Client Reporting
- `POST /api/report` - Submit system report
- `GET /api/clients` - List all clients
- `GET /api/client/<id>` - Get specific client details
- `GET /api/stats` - Get system statistics

## 🔧 Configuration

### Server Configuration
Edit `System Monitor Central Server.py`:
- Database location
- Port settings
- Security options

### Client Configuration
Command line options:
```bash
python "System Monitor Client.py" [options]

Options:
  --server URL          Server URL (default: http://localhost:5000)
  --client-id ID        Custom client identifier
  --interval MINUTES    Reporting interval (default: 60)
  --once               Run once and exit
  --local-only         Save locally only
```

## 📁 File Structure

```
System Monitor/
├── System Monitor Central Server.py    # Main server application
├── System Monitor Client.py            # Client agent
├── Requirements File.txt               # Python dependencies
├── Linux Installation Script.sh       # Linux installer
├── Windows Client Installer.bat       # Windows installer
├── start_system.bat                   # Windows startup script
├── start_system.sh                    # Linux startup script
└── README.md                          # This documentation
```

## 🔒 Security Considerations

### Production Deployment
1. **Change default ports** - Don't use port 5000 in production
2. **Enable HTTPS** - Use SSL/TLS certificates
3. **Authentication** - Implement user authentication
4. **Firewall Rules** - Restrict access to management ports
5. **Database Security** - Use proper database permissions

### Network Security
- Use VPN for remote clients
- Implement API key authentication
- Regular security updates

## 🚀 Advanced Features

### Scaling
- **Multiple Servers** - Load balancing support
- **Database Clustering** - PostgreSQL/MySQL support
- **Distributed Deployment** - Multi-site management

### Monitoring
- **Alerting** - Email/SMS notifications
- **Thresholds** - Custom performance alerts
- **Reporting** - Automated reports
- **Integration** - Third-party tool integration

## 🛠️ Troubleshooting

### Common Issues

**Client can't connect to server:**
1. Check network connectivity
2. Verify server is running
3. Check firewall settings
4. Validate server URL

**Service won't start:**
1. Check Python installation
2. Verify dependencies installed
3. Check file permissions
4. Review system logs

### Logs
- **Server logs** - Console output or log files
- **Client logs** - Service logs or console output
- **System logs** - Windows Event Viewer / Linux syslog

## 📞 Support

For issues and questions:
1. Check this documentation
2. Review log files
3. Verify system requirements
4. Check network connectivity

## 🔄 Updates

To update the system:
1. Stop all services
2. Update files
3. Restart services
4. Verify functionality

---

**System Monitor** - Enterprise System Management Made Simple

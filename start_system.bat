@echo off
REM System Monitor - Production Startup Script
REM This script starts both the server and client for testing

echo ========================================
echo System Monitor - Production Startup
echo ========================================
echo.

REM Check if virtual environment exists
if not exist ".venv" (
    echo Creating virtual environment...
    python -m venv .venv
    echo Installing dependencies...
    .venv\Scripts\pip install psutil requests flask
)

echo Starting System Monitor Server...
start "System Monitor Server" cmd /k ".venv\Scripts\python \"System Monitor Central Server.py\""

echo Waiting for server to start...
timeout /t 5 /nobreak >nul

echo Starting System Monitor Client (continuous monitoring)...
start "System Monitor Client" cmd /k ".venv\Scripts\python \"System Monitor Client.py\" --interval 5"

echo.
echo ========================================
echo System Monitor Started Successfully!
echo ========================================
echo.
echo Server Console: http://localhost:5000
echo.
echo To stop the system:
echo 1. Close both command windows
echo 2. Or press Ctrl+C in each window
echo.
pause

(['C:\\Users\\<USER>\\Desktop\\computer\\System Monitor Client.py'],
 ['C:\\Users\\<USER>\\Desktop\\computer'],
 [],
 [('c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('System Monitor Client',
   'C:\\Users\\<USER>\\Desktop\\computer\\System Monitor Client.py',
   'PYSOURCE')],
 [('pkg_resources',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.tags',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('struct', 'C:\\Python312\\Lib\\struct.py', 'PYMODULE'),
  ('logging', 'C:\\Python312\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'C:\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python312\\Lib\\dataclasses.py', 'PYMODULE'),
  ('copy', 'C:\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python312\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('threading', 'C:\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python312\\Lib\\_threading_local.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python312\\Lib\\contextlib.py', 'PYMODULE'),
  ('string', 'C:\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('packaging.metadata',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('email.policy', 'C:\\Python312\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email', 'C:\\Python312\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'C:\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'C:\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('urllib', 'C:\\Python312\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python312\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'C:\\Python312\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('quopri', 'C:\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('email.quoprimime', 'C:\\Python312\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.base64mime', 'C:\\Python312\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python312\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.utils', 'C:\\Python312\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr', 'C:\\Python312\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'C:\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python312\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('random', 'C:\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'C:\\Python312\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'C:\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python312\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python312\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'C:\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'C:\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.message', 'C:\\Python312\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.iterators', 'C:\\Python312\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python312\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'C:\\Python312\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\Python312\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('packaging.licenses._spdx',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('ast', 'C:\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('packaging._musllinux',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('ctypes', 'C:\\Python312\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'C:\\Python312\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('packaging._elffile',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('sysconfig', 'C:\\Python312\\Lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Python312\\Lib\\_aix_support.py', 'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('py_compile', 'C:\\Python312\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('csv', 'C:\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tokenize', 'C:\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'C:\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('setuptools._distutils.spawn',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('shutil', 'C:\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'C:\\Python312\\Lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'C:\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'C:\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('setuptools._distutils._modified',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('importlib.util', 'C:\\Python312\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc', 'C:\\Python312\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('unittest.mock', 'C:\\Python312\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest', 'C:\\Python312\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\Python312\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals', 'C:\\Python312\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('signal', 'C:\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('unittest.main', 'C:\\Python312\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner', 'C:\\Python312\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.loader', 'C:\\Python312\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.suite', 'C:\\Python312\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.case', 'C:\\Python312\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'C:\\Python312\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('difflib', 'C:\\Python312\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result', 'C:\\Python312\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.util', 'C:\\Python312\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('asyncio', 'C:\\Python312\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Python312\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'C:\\Python312\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('selectors', 'C:\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Python312\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Python312\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Python312\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl', 'C:\\Python312\\Lib\\ssl.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Python312\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Python312\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads', 'C:\\Python312\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Python312\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Python312\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams', 'C:\\Python312\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.queues', 'C:\\Python312\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'C:\\Python312\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Python312\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Python312\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Python312\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('queue', 'C:\\Python312\\Lib\\queue.py', 'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Python312\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Python312\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Python312\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Python312\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Python312\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'C:\\Python312\\Lib\\runpy.py', 'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Python312\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Python312\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Python312\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Python312\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python312\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python312\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python312\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Python312\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Python312\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Python312\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Python312\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Python312\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Python312\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Python312\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'C:\\Python312\\Lib\\secrets.py', 'PYMODULE'),
  ('hmac', 'C:\\Python312\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Python312\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Python312\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Python312\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Python312\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client', 'C:\\Python312\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'C:\\Python312\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Python312\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers', 'C:\\Python312\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'C:\\Python312\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Python312\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'C:\\Python312\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('urllib.request', 'C:\\Python312\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'C:\\Python312\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python312\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python312\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'C:\\Python312\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Python312\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\Python312\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'C:\\Python312\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('urllib.response', 'C:\\Python312\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python312\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'C:\\Python312\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'C:\\Python312\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Python312\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Python312\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'C:\\Python312\\Lib\\http\\client.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Python312\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Python312\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'C:\\Python312\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('asyncio.trsock', 'C:\\Python312\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Python312\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts', 'C:\\Python312\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.tasks', 'C:\\Python312\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Python312\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks', 'C:\\Python312\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.mixins', 'C:\\Python312\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.sslproto', 'C:\\Python312\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.transports',
   'C:\\Python312\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Python312\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures', 'C:\\Python312\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Python312\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Python312\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events', 'C:\\Python312\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Python312\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Python312\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Python312\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._distutils',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('site', 'C:\\Python312\\Lib\\site.py', 'PYMODULE'),
  ('rlcompleter', 'C:\\Python312\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', 'C:\\Python312\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', 'C:\\Python312\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Python312\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'C:\\Python312\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server', 'C:\\Python312\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'C:\\Python312\\Lib\\socketserver.py', 'PYMODULE'),
  ('html', 'C:\\Python312\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\Python312\\Lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Python312\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data', 'C:\\Python312\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('tty', 'C:\\Python312\\Lib\\tty.py', 'PYMODULE'),
  ('setuptools._distutils.core',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser', 'C:\\Python312\\Lib\\configparser.py', 'PYMODULE'),
  ('setuptools._distutils.cmd',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.glob',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib', 'C:\\Python312\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'C:\\Python312\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._types', 'C:\\Python312\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tomllib._re', 'C:\\Python312\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('glob', 'C:\\Python312\\Lib\\glob.py', 'PYMODULE'),
  ('setuptools._shutil',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('ctypes.wintypes', 'C:\\Python312\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('setuptools.command',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('dis', 'C:\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('setuptools._imp',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_distutils_hack',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.utils',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.markers',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('typing', 'C:\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('zipimport', 'C:\\Python312\\Lib\\zipimport.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python312\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('textwrap', 'C:\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('tempfile', 'C:\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('plistlib', 'C:\\Python312\\Lib\\plistlib.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Python312\\Lib\\pkgutil.py', 'PYMODULE'),
  ('inspect', 'C:\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.abc', 'C:\\Python312\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib', 'C:\\Python312\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python312\\Lib\\email\\parser.py', 'PYMODULE'),
  ('__future__', 'C:\\Python312\\Lib\\__future__.py', 'PYMODULE'),
  ('pathlib', 'C:\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python312\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python312\\Lib\\stringprep.py', 'PYMODULE'),
  ('argparse', 'C:\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('datetime', 'C:\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Python312\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('requests',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies', 'C:\\Python312\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('requests.models',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('subprocess', 'C:\\Python312\\Lib\\subprocess.py', 'PYMODULE'),
  ('socket', 'C:\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('psutil',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('platform', 'C:\\Python312\\Lib\\platform.py', 'PYMODULE'),
  ('json', 'C:\\Python312\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python312\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python312\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python312\\Lib\\json\\scanner.py', 'PYMODULE')],
 [('python312.dll', 'C:\\Python312\\python312.dll', 'BINARY'),
  ('unicodedata.pyd', 'C:\\Python312\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'C:\\Python312\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python312\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Python312\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'C:\\Python312\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python312\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python312\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Python312\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Python312\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python312\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'C:\\Python312\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\Python312\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-3.dll', 'C:\\Python312\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libffi-8.dll', 'C:\\Python312\\DLLs\\libffi-8.dll', 'BINARY'),
  ('libssl-3.dll', 'C:\\Python312\\DLLs\\libssl-3.dll', 'BINARY'),
  ('python3.dll', 'C:\\Python312\\python3.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Python312\\VCRUNTIME140_1.dll', 'BINARY')],
 [],
 [],
 [('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('certifi\\cacert.pem',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'c:\\Users\\<USER>\\Desktop\\computer\\.venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\computer\\build\\SystemMonitorClient\\base_library.zip',
   'DATA')])

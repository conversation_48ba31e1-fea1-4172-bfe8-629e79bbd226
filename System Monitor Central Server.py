#!/usr/bin/env python3

"""
System Monitor Central Server - Receives and manages client reports
Similar to SCCM console functionality

"""

from flask import Flask, request, jsonify, render_template_string
import sqlite3
import json
from datetime import datetime, timedelta
import os
import threading
import time

app = Flask(__name__)

# Database setup
DATABASE = 'system_monitor.db'

def init_database():
    """Initialize SQLite database"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    
    # Create clients table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id TEXT UNIQUE NOT NULL,
            computer_name TEXT,
            last_seen TIMESTAMP,
            platform TEXT,
            platform_version TEXT,
            architecture TEXT,
            processor TEXT,
            status TEXT DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create system_reports table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS system_reports (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id TEXT,
            computer_name TEXT,
            timestamp TIMESTAMP,
            platform TEXT,
            platform_version TEXT,
            architecture TEXT,
            processor TEXT,
            memory_total_gb REAL,
            memory_used_gb REAL,
            memory_percentage REAL,
            storage_info TEXT,
            network_info TEXT,
            uptime_days INTEGER,
            cpu_cores INTEGER,
            cpu_usage_percent REAL,
            raw_data TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients (client_id)
        )
    ''')
    
    conn.commit()
    conn.close()

def update_client_status():
    """Update client status based on last seen time"""
    while True:
        try:
            conn = sqlite3.connect(DATABASE)
            cursor = conn.cursor()
            
            # Mark clients as offline if not seen in last 2 hours
            offline_threshold = datetime.now() - timedelta(hours=2)
            cursor.execute('''
                UPDATE clients 
                SET status = 'offline' 
                WHERE last_seen < ? AND status = 'active'
            ''', (offline_threshold,))
            
            conn.commit()
            conn.close()
            
            time.sleep(300)  # Check every 5 minutes
        except Exception as e:
            print(f"Error updating client status: {e}")
            time.sleep(300)

# Start background thread for status updates
status_thread = threading.Thread(target=update_client_status, daemon=True)
status_thread.start()

@app.route('/api/report', methods=['POST'])
def receive_report():
    """Receive system report from client"""
    try:
        data = request.get_json()
        
        if not data or 'client_id' not in data:
            return jsonify({'error': 'Invalid data'}), 400
        
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        # Update or insert client
        cursor.execute('''
            INSERT OR REPLACE INTO clients (
                client_id, computer_name, last_seen, platform, 
                platform_version, architecture, processor, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active')
        ''', (
            data['client_id'],
            data.get('computer_name', ''),
            data.get('timestamp', datetime.now().isoformat()),
            data.get('platform', ''),
            data.get('platform_version', ''),
            data.get('architecture', ''),
            data.get('processor', '')
        ))
        
        # Insert system report
        memory = data.get('memory', {})
        storage_info = json.dumps(data.get('storage', []))
        network_info = json.dumps(data.get('network', []))
        uptime = data.get('uptime', {})
        cpu = data.get('cpu', {})
        
        cursor.execute('''
            INSERT INTO system_reports (
                client_id, computer_name, timestamp, platform, platform_version,
                architecture, processor, memory_total_gb, memory_used_gb, 
                memory_percentage, storage_info, network_info, uptime_days,
                cpu_cores, cpu_usage_percent, raw_data
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['client_id'],
            data.get('computer_name', ''),
            data.get('timestamp', datetime.now().isoformat()),
            data.get('platform', ''),
            data.get('platform_version', ''),
            data.get('architecture', ''),
            data.get('processor', ''),
            memory.get('total_gb', 0),
            memory.get('used_gb', 0),
            memory.get('percentage', 0),
            storage_info,
            network_info,
            uptime.get('uptime_days', 0),
            cpu.get('logical_cores', 0),
            cpu.get('usage_percent', 0),
            json.dumps(data)
        ))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'status': 'success',
            'message': f'Report received for {data["client_id"]}',
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/clients')
def get_clients():
    """Get list of all clients"""
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT client_id, computer_name, last_seen, platform, 
                   platform_version, status, created_at
            FROM clients 
            ORDER BY last_seen DESC
        ''')
        
        clients = []
        for row in cursor.fetchall():
            clients.append({
                'client_id': row[0],
                'computer_name': row[1],
                'last_seen': row[2],
                'platform': row[3],
                'platform_version': row[4],
                'status': row[5],
                'created_at': row[6]
            })
        
        conn.close()
        return jsonify(clients)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/client/<client_id>')
def get_client_details(client_id):
    """Get detailed information for specific client"""
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        # Get latest report
        cursor.execute('''
            SELECT * FROM system_reports 
            WHERE client_id = ? 
            ORDER BY timestamp DESC 
            LIMIT 1
        ''', (client_id,))
        
        row = cursor.fetchone()
        if not row:
            return jsonify({'error': 'Client not found'}), 404
        
        # Parse the data
        report = {
            'client_id': row[1],
            'computer_name': row[2],
            'timestamp': row[3],
            'platform': row[4],
            'platform_version': row[5],
            'architecture': row[6],
            'processor': row[7],
            'memory': {
                'total_gb': row[8],
                'used_gb': row[9],
                'percentage': row[10]
            },
            'storage': json.loads(row[11]) if row[11] else [],
            'network': json.loads(row[12]) if row[12] else [],
            'uptime_days': row[13],
            'cpu': {
                'cores': row[14],
                'usage_percent': row[15]
            }
        }
        
        conn.close()
        return jsonify(report)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats')
def get_statistics():
    """Get overall system statistics"""
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        # Total clients
        cursor.execute('SELECT COUNT(*) FROM clients')
        total_clients = cursor.fetchone()[0]
        
        # Active clients
        cursor.execute('SELECT COUNT(*) FROM clients WHERE status = "active"')
        active_clients = cursor.fetchone()[0]
        
        # Platform distribution
        cursor.execute('''
            SELECT platform, COUNT(*) 
            FROM clients 
            GROUP BY platform
        ''')
        platform_stats = dict(cursor.fetchall())
        
        # Memory statistics
        cursor.execute('''
            SELECT AVG(memory_total_gb), AVG(memory_percentage)
            FROM system_reports 
            WHERE timestamp > datetime('now', '-1 day')
        ''')
        memory_stats = cursor.fetchone()
        
        conn.close()
        
        return jsonify({
            'total_clients': total_clients,
            'active_clients': active_clients,
            'offline_clients': total_clients - active_clients,
            'platform_distribution': platform_stats,
            'average_memory_gb': round(memory_stats[0] or 0, 2),
            'average_memory_usage': round(memory_stats[1] or 0, 2)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Web Console HTML Template
CONSOLE_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>System Monitor Console</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .header { background: #2c3e50; color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .stats { display: flex; gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 15px; border-radius: 5px; flex: 1; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-number { font-size: 24px; font-weight: bold; color: #3498db; }
        .clients-table { background: white; border-radius: 5px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #34495e; color: white; }
        .status-active { color: #27ae60; font-weight: bold; }
        .status-offline { color: #e74c3c; font-weight: bold; }
        .refresh-btn { background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
        .client-details { background: white; margin-top: 20px; padding: 15px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="header">
        <h1>System Monitor Console</h1>
        <p>Central management for system monitoring clients</p>
    </div>
    
    <div class="stats" id="stats">
        <div class="stat-card">
            <div class="stat-number" id="total-clients">-</div>
            <div>Total Clients</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="active-clients">-</div>
            <div>Active Clients</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="offline-clients">-</div>
            <div>Offline Clients</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="avg-memory">-</div>
            <div>Avg Memory (GB)</div>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="loadData()">Refresh Data</button>
    
    <div class="clients-table">
        <table>
            <thead>
                <tr>
                    <th>Client ID</th>
                    <th>Computer Name</th>
                    <th>Platform</th>
                    <th>Last Seen</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="clients-tbody">
            </tbody>
        </table>
    </div>
    
    <div id="client-details" class="client-details" style="display: none;">
        <h3>Client Details</h3>
        <div id="details-content"></div>
    </div>
    
    <script>
        function loadData() {
            // Load statistics
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('total-clients').textContent = data.total_clients;
                    document.getElementById('active-clients').textContent = data.active_clients;
                    document.getElementById('offline-clients').textContent = data.offline_clients;
                    document.getElementById('avg-memory').textContent = data.average_memory_gb;
                });
            
            // Load clients
            fetch('/api/clients')
                .then(response => response.json())
                .then(data => {
                    const tbody = document.getElementById('clients-tbody');
                    tbody.innerHTML = '';
                    
                    data.forEach(client => {
                        const row = tbody.insertRow();
                        row.innerHTML = `
                            <td>${client.client_id}</td>
                            <td>${client.computer_name}</td>
                            <td>${client.platform} ${client.platform_version}</td>
                            <td>${new Date(client.last_seen).toLocaleString()}</td>
                            <td><span class="status-${client.status}">${client.status.toUpperCase()}</span></td>
                            <td><button onclick="showClientDetails('${client.client_id}')">Details</button></td>
                        `;
                    });
                });
        }
        
        function showClientDetails(clientId) {
            fetch(`/api/client/${clientId}`)
                .then(response => response.json())
                .then(data => {
                    const detailsDiv = document.getElementById('client-details');
                    const contentDiv = document.getElementById('details-content');
                    
                    let storageHtml = '<h4>Storage Devices:</h4><ul>';
                    data.storage.forEach(storage => {
                        storageHtml += `<li>${storage.device}: ${storage.total_gb} GB (${storage.percentage}% used)</li>`;
                    });
                    storageHtml += '</ul>';
                    
                    contentDiv.innerHTML = `
                        <h4>System Information</h4>
                        <p><strong>Computer:</strong> ${data.computer_name}</p>
                        <p><strong>Platform:</strong> ${data.platform_version}</p>
                        <p><strong>Architecture:</strong> ${data.architecture}</p>
                        <p><strong>Processor:</strong> ${data.processor}</p>
                        <p><strong>Memory:</strong> ${data.memory.total_gb} GB (${data.memory.percentage}% used)</p>
                        <p><strong>CPU Cores:</strong> ${data.cpu.cores} (${data.cpu.usage_percent}% usage)</p>
                        <p><strong>Uptime:</strong> ${data.uptime_days} days</p>
                        <p><strong>Last Updated:</strong> ${new Date(data.timestamp).toLocaleString()}</p>
                        ${storageHtml}
                    `;
                    
                    detailsDiv.style.display = 'block';
                });
        }
        
        // Load data on page load
        loadData();
        
        // Auto-refresh every 30 seconds
        setInterval(loadData, 30000);
    </script>
</body>
</html>
'''

@app.route('/')
def console():
    """Main console interface"""
    return render_template_string(CONSOLE_TEMPLATE)

if __name__ == '__main__':
    init_database()
    print("System Monitor Server starting...")
    print("Console available at: http://localhost:5000")
    print("API endpoints:")
    print("  POST /api/report - Receive client reports")
    print("  GET /api/clients - List all clients")
    print("  GET /api/client/<id> - Get client details")
    print("  GET /api/stats - Get statistics")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
#!/usr/bin/env python3

"""
System Monitor Central Server - Receives and manages client reports
Similar to SCCM console functionality

"""

from flask import Flask, request, jsonify, render_template_string
import sqlite3
import json
from datetime import datetime, timedelta
import os
import threading
import time

app = Flask(__name__)

# Database setup
DATABASE = 'system_monitor.db'

def init_database():
    """Initialize SQLite database"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()

    # Create clients table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id TEXT UNIQUE NOT NULL,
            computer_name TEXT,
            last_seen TIMESTAMP,
            platform TEXT,
            platform_version TEXT,
            architecture TEXT,
            processor TEXT,
            status TEXT DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Create system_reports table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS system_reports (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id TEXT,
            computer_name TEXT,
            timestamp TIMESTAMP,
            platform TEXT,
            platform_version TEXT,
            architecture TEXT,
            processor TEXT,
            memory_total_gb REAL,
            memory_used_gb REAL,
            memory_percentage REAL,
            storage_info TEXT,
            network_info TEXT,
            uptime_days INTEGER,
            cpu_cores INTEGER,
            cpu_usage_percent REAL,
            raw_data TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients (client_id)
        )
    ''')

    conn.commit()
    conn.close()

def update_client_status():
    """Update client status based on last seen time"""
    while True:
        try:
            conn = sqlite3.connect(DATABASE)
            cursor = conn.cursor()

            # Mark clients as offline if not seen in last 2 hours
            offline_threshold = datetime.now() - timedelta(hours=2)
            cursor.execute('''
                UPDATE clients
                SET status = 'offline'
                WHERE last_seen < ? AND status = 'active'
            ''', (offline_threshold,))

            conn.commit()
            conn.close()

            time.sleep(300)  # Check every 5 minutes
        except Exception as e:
            print(f"Error updating client status: {e}")
            time.sleep(300)

# Start background thread for status updates
status_thread = threading.Thread(target=update_client_status, daemon=True)
status_thread.start()

@app.route('/api/report', methods=['POST'])
def receive_report():
    """Receive system report from client"""
    try:
        data = request.get_json()

        if not data or 'client_id' not in data:
            return jsonify({'error': 'Invalid data'}), 400

        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        # Update or insert client
        cursor.execute('''
            INSERT OR REPLACE INTO clients (
                client_id, computer_name, last_seen, platform,
                platform_version, architecture, processor, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active')
        ''', (
            data['client_id'],
            data.get('computer_name', ''),
            data.get('timestamp', datetime.now().isoformat()),
            data.get('platform', ''),
            data.get('platform_version', ''),
            data.get('architecture', ''),
            data.get('processor', '')
        ))

        # Insert system report
        memory = data.get('memory', {})
        storage_info = json.dumps(data.get('storage', []))
        network_info = json.dumps(data.get('network', []))
        uptime = data.get('uptime', {})
        cpu = data.get('cpu', {})

        cursor.execute('''
            INSERT INTO system_reports (
                client_id, computer_name, timestamp, platform, platform_version,
                architecture, processor, memory_total_gb, memory_used_gb,
                memory_percentage, storage_info, network_info, uptime_days,
                cpu_cores, cpu_usage_percent, raw_data
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['client_id'],
            data.get('computer_name', ''),
            data.get('timestamp', datetime.now().isoformat()),
            data.get('platform', ''),
            data.get('platform_version', ''),
            data.get('architecture', ''),
            data.get('processor', ''),
            memory.get('total_gb', 0),
            memory.get('used_gb', 0),
            memory.get('percentage', 0),
            storage_info,
            network_info,
            uptime.get('uptime_days', 0),
            cpu.get('logical_cores', 0),
            cpu.get('usage_percent', 0),
            json.dumps(data)
        ))

        conn.commit()
        conn.close()

        return jsonify({
            'status': 'success',
            'message': f'Report received for {data["client_id"]}',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/clients')
def get_clients():
    """Get list of all clients"""
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT client_id, computer_name, last_seen, platform,
                   platform_version, status, created_at
            FROM clients
            ORDER BY last_seen DESC
        ''')

        clients = []
        for row in cursor.fetchall():
            clients.append({
                'client_id': row[0],
                'computer_name': row[1],
                'last_seen': row[2],
                'platform': row[3],
                'platform_version': row[4],
                'status': row[5],
                'created_at': row[6]
            })

        conn.close()
        return jsonify(clients)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/client/<client_id>')
def get_client_details(client_id):
    """Get detailed information for specific client"""
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        # Get latest report
        cursor.execute('''
            SELECT * FROM system_reports
            WHERE client_id = ?
            ORDER BY timestamp DESC
            LIMIT 1
        ''', (client_id,))

        row = cursor.fetchone()
        if not row:
            return jsonify({'error': 'Client not found'}), 404

        # Parse the data
        report = {
            'client_id': row[1],
            'computer_name': row[2],
            'timestamp': row[3],
            'platform': row[4],
            'platform_version': row[5],
            'architecture': row[6],
            'processor': row[7],
            'memory': {
                'total_gb': row[8],
                'used_gb': row[9],
                'percentage': row[10]
            },
            'storage': json.loads(row[11]) if row[11] else [],
            'network': json.loads(row[12]) if row[12] else [],
            'uptime_days': row[13],
            'cpu': {
                'cores': row[14],
                'usage_percent': row[15]
            }
        }

        # Extract BIOS information from raw_data if available
        try:
            if row[16]:  # raw_data field
                raw_data = json.loads(row[16])
                if 'bios' in raw_data:
                    report['bios'] = raw_data['bios']
                else:
                    report['bios'] = {
                        'version': 'Unknown',
                        'date': 'Unknown',
                        'vendor': 'Unknown',
                        'serial_number': 'Unknown'
                    }
            else:
                report['bios'] = {
                    'version': 'Unknown',
                    'date': 'Unknown',
                    'vendor': 'Unknown',
                    'serial_number': 'Unknown'
                }
        except (json.JSONDecodeError, KeyError):
            report['bios'] = {
                'version': 'Unknown',
                'date': 'Unknown',
                'vendor': 'Unknown',
                'serial_number': 'Unknown'
            }

        conn.close()
        return jsonify(report)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats')
def get_statistics():
    """Get overall system statistics"""
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        # Total clients
        cursor.execute('SELECT COUNT(*) FROM clients')
        total_clients = cursor.fetchone()[0]

        # Active clients
        cursor.execute('SELECT COUNT(*) FROM clients WHERE status = "active"')
        active_clients = cursor.fetchone()[0]

        # Platform distribution
        cursor.execute('''
            SELECT platform, COUNT(*)
            FROM clients
            GROUP BY platform
        ''')
        platform_stats = dict(cursor.fetchall())

        # Memory statistics
        cursor.execute('''
            SELECT AVG(memory_total_gb), AVG(memory_percentage)
            FROM system_reports
            WHERE timestamp > datetime('now', '-1 day')
        ''')
        memory_stats = cursor.fetchone()

        conn.close()

        return jsonify({
            'total_clients': total_clients,
            'active_clients': active_clients,
            'offline_clients': total_clients - active_clients,
            'platform_distribution': platform_stats,
            'average_memory_gb': round(memory_stats[0] or 0, 2),
            'average_memory_usage': round(memory_stats[1] or 0, 2)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Web Console HTML Template
CONSOLE_TEMPLATE = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Monitor Console</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            color: white;
        }

        .btn-primary {
            background: linear-gradient(45deg, #3498db, #2980b9);
        }

        .btn-success {
            background: linear-gradient(45deg, #27ae60, #229954);
        }

        .btn-warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }

        .btn-danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stat-icon {
            font-size: 2rem;
            opacity: 0.7;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-trend {
            font-size: 0.8rem;
            margin-top: 10px;
        }

        .trend-up { color: #27ae60; }
        .trend-down { color: #e74c3c; }

        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .table-header {
            background: linear-gradient(45deg, #34495e, #2c3e50);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .search-box {
            padding: 8px 15px;
            border: none;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            placeholder-color: rgba(255, 255, 255, 0.7);
            width: 250px;
        }

        .search-box::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            position: sticky;
            top: 0;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background: #d5f4e6;
            color: #27ae60;
        }

        .status-offline {
            background: #fadbd8;
            color: #e74c3c;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            margin: 0 2px;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            transform: translateY(-1px);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 25px;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
        }

        .close:hover {
            opacity: 0.7;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .detail-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .detail-section h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #7f8c8d;
        }

        .detail-value {
            color: #2c3e50;
            text-align: right;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #3498db, #2980b9);
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .btn-group {
                justify-content: center;
            }

            .stats {
                grid-template-columns: 1fr;
            }

            .search-box {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-desktop"></i> System Monitor Console</h1>
            <p>Enterprise-grade system monitoring and management platform</p>
        </div>

        <div class="controls">
            <div class="btn-group">
                <button class="btn btn-primary" onclick="loadData()">
                    <i class="fas fa-sync-alt"></i> Refresh Data
                </button>
                <button class="btn btn-success" onclick="exportData()">
                    <i class="fas fa-download"></i> Export Data
                </button>
                <button class="btn btn-warning" onclick="showSettings()">
                    <i class="fas fa-cog"></i> Settings
                </button>
            </div>
            <div class="alert alert-info" id="last-update" style="display: none;">
                <i class="fas fa-clock"></i>
                <span>Last updated: <span id="update-time">Never</span></span>
            </div>
        </div>

        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon" style="color: #3498db;">
                        <i class="fas fa-server"></i>
                    </div>
                </div>
                <div class="stat-number" id="total-clients" style="color: #3498db;">-</div>
                <div class="stat-label">Total Clients</div>
                <div class="stat-trend trend-up" id="total-trend">
                    <i class="fas fa-arrow-up"></i> +0 this hour
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon" style="color: #27ae60;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="stat-number" id="active-clients" style="color: #27ae60;">-</div>
                <div class="stat-label">Active Clients</div>
                <div class="stat-trend trend-up" id="active-trend">
                    <i class="fas fa-heartbeat"></i> Online now
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon" style="color: #e74c3c;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="stat-number" id="offline-clients" style="color: #e74c3c;">-</div>
                <div class="stat-label">Offline Clients</div>
                <div class="stat-trend trend-down" id="offline-trend">
                    <i class="fas fa-times-circle"></i> Need attention
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon" style="color: #f39c12;">
                        <i class="fas fa-memory"></i>
                    </div>
                </div>
                <div class="stat-number" id="avg-memory" style="color: #f39c12;">-</div>
                <div class="stat-label">Avg Memory (GB)</div>
                <div class="stat-trend" id="memory-trend">
                    <i class="fas fa-chart-line"></i> System average
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="table-header">
                <h3><i class="fas fa-list"></i> Connected Clients</h3>
                <input type="text" class="search-box" id="search-box" placeholder="Search clients..." onkeyup="filterClients()">
            </div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th><i class="fas fa-desktop"></i> Client ID</th>
                            <th><i class="fas fa-computer"></i> Computer Name</th>
                            <th><i class="fas fa-laptop"></i> Platform</th>
                            <th><i class="fas fa-clock"></i> Last Seen</th>
                            <th><i class="fas fa-signal"></i> Status</th>
                            <th><i class="fas fa-tools"></i> Actions</th>
                        </tr>
                    </thead>
                    <tbody id="clients-tbody">
                        <tr>
                            <td colspan="6" class="loading">
                                <div class="spinner"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Client Details Modal -->
    <div id="client-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-info-circle"></i> Client Details</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body" id="modal-body">
                <div class="loading">
                    <div class="spinner"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let clientsData = [];
        let lastUpdateTime = null;

        function loadData() {
            showLoading();

            // Load statistics
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('total-clients').textContent = data.total_clients;
                    document.getElementById('active-clients').textContent = data.active_clients;
                    document.getElementById('offline-clients').textContent = data.offline_clients;
                    document.getElementById('avg-memory').textContent = data.average_memory_gb;

                    // Update trends
                    updateTrends(data);
                })
                .catch(error => {
                    console.error('Error loading stats:', error);
                    showError('Failed to load statistics');
                });

            // Load clients
            fetch('/api/clients')
                .then(response => response.json())
                .then(data => {
                    clientsData = data;
                    renderClientsTable(data);
                    updateLastUpdateTime();
                    hideLoading();
                })
                .catch(error => {
                    console.error('Error loading clients:', error);
                    showError('Failed to load client data');
                    hideLoading();
                });
        }

        function renderClientsTable(clients) {
            const tbody = document.getElementById('clients-tbody');
            tbody.innerHTML = '';

            if (clients.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 40px;">
                            <i class="fas fa-inbox" style="font-size: 3rem; color: #bdc3c7; margin-bottom: 15px;"></i>
                            <p style="color: #7f8c8d; font-size: 1.1rem;">No clients connected yet</p>
                            <p style="color: #95a5a6; font-size: 0.9rem;">Install the client agent on your systems to start monitoring</p>
                        </td>
                    </tr>
                `;
                return;
            }

            clients.forEach(client => {
                const row = tbody.insertRow();
                const lastSeen = new Date(client.last_seen);
                const timeDiff = Math.floor((new Date() - lastSeen) / (1000 * 60)); // minutes ago

                let timeDisplay = lastSeen.toLocaleString();
                if (timeDiff < 60) {
                    timeDisplay = `${timeDiff} min ago`;
                } else if (timeDiff < 1440) {
                    timeDisplay = `${Math.floor(timeDiff / 60)} hours ago`;
                }

                row.innerHTML = `
                    <td>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-desktop" style="color: #3498db;"></i>
                            <span>${client.client_id}</span>
                        </div>
                    </td>
                    <td>
                        <strong>${client.computer_name}</strong>
                    </td>
                    <td>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <i class="fab fa-${getPlatformIcon(client.platform)}" style="color: #7f8c8d;"></i>
                            <span>${client.platform} ${client.platform_version}</span>
                        </div>
                    </td>
                    <td>
                        <div style="font-size: 0.9rem;">
                            <div>${timeDisplay}</div>
                            <div style="color: #7f8c8d; font-size: 0.8rem;">${lastSeen.toLocaleDateString()}</div>
                        </div>
                    </td>
                    <td>
                        <span class="status-badge status-${client.status}">
                            <i class="fas fa-${client.status === 'active' ? 'check-circle' : 'times-circle'}"></i>
                            ${client.status.toUpperCase()}
                        </span>
                    </td>
                    <td>
                        <button class="action-btn btn-primary" onclick="showClientDetails('${client.client_id}')" title="View Details">
                            <i class="fas fa-info-circle"></i>
                        </button>
                        <button class="action-btn btn-success" onclick="pingClient('${client.client_id}')" title="Ping Client">
                            <i class="fas fa-satellite-dish"></i>
                        </button>
                        <button class="action-btn btn-warning" onclick="exportClientData('${client.client_id}')" title="Export Data">
                            <i class="fas fa-download"></i>
                        </button>
                    </td>
                `;
            });
        }

        function showClientDetails(clientId) {
            const modal = document.getElementById('client-modal');
            const modalBody = document.getElementById('modal-body');

            modal.style.display = 'block';
            modalBody.innerHTML = '<div class="loading"><div class="spinner"></div></div>';

            fetch(`/api/client/${clientId}`)
                .then(response => response.json())
                .then(data => {
                    modalBody.innerHTML = generateClientDetailsHTML(data);
                })
                .catch(error => {
                    console.error('Error loading client details:', error);
                    modalBody.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            Failed to load client details: ${error.message}
                        </div>
                    `;
                });
        }

        function generateClientDetailsHTML(data) {
            const memoryUsagePercent = data.memory.percentage || 0;
            const cpuUsagePercent = data.cpu.usage_percent || 0;

            let storageHTML = '';
            if (data.storage && data.storage.length > 0) {
                storageHTML = data.storage.map(storage => `
                    <div class="detail-item">
                        <span class="detail-label">${storage.device}</span>
                        <div class="detail-value">
                            <div>${storage.total_gb} GB total</div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${storage.percentage}%"></div>
                            </div>
                            <div style="font-size: 0.8rem; color: #7f8c8d;">${storage.percentage}% used</div>
                        </div>
                    </div>
                `).join('');
            }

            let networkHTML = '';
            if (data.network && data.network.length > 0) {
                networkHTML = data.network.map(net => `
                    <div class="detail-item">
                        <span class="detail-label">${net.interface}</span>
                        <span class="detail-value">${net.ip_address}</span>
                    </div>
                `).join('');
            }

            return `
                <div class="detail-grid">
                    <div class="detail-section">
                        <h4><i class="fas fa-info-circle"></i> System Information</h4>
                        <div class="detail-item">
                            <span class="detail-label">Computer Name</span>
                            <span class="detail-value">${data.computer_name}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Platform</span>
                            <span class="detail-value">${data.platform_version}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Architecture</span>
                            <span class="detail-value">${data.architecture}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Processor</span>
                            <span class="detail-value">${data.processor}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Uptime</span>
                            <span class="detail-value">${data.uptime_days} days</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Last Updated</span>
                            <span class="detail-value">${new Date(data.timestamp).toLocaleString()}</span>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4><i class="fas fa-microchip"></i> BIOS Information</h4>
                        <div class="detail-item">
                            <span class="detail-label">BIOS Version</span>
                            <span class="detail-value">${data.bios ? data.bios.version : 'Unknown'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">BIOS Date</span>
                            <span class="detail-value">${data.bios ? data.bios.date : 'Unknown'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">BIOS Vendor</span>
                            <span class="detail-value">${data.bios ? data.bios.vendor : 'Unknown'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Serial Number</span>
                            <span class="detail-value">${data.bios ? data.bios.serial_number : 'Unknown'}</span>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4><i class="fas fa-memory"></i> Memory & CPU</h4>
                        <div class="detail-item">
                            <span class="detail-label">Total Memory</span>
                            <span class="detail-value">${data.memory.total_gb} GB</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Memory Usage</span>
                            <div class="detail-value">
                                <div>${data.memory.used_gb} GB used</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${memoryUsagePercent}%"></div>
                                </div>
                                <div style="font-size: 0.8rem; color: #7f8c8d;">${memoryUsagePercent}%</div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">CPU Cores</span>
                            <span class="detail-value">${data.cpu.cores}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">CPU Usage</span>
                            <div class="detail-value">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${cpuUsagePercent}%"></div>
                                </div>
                                <div style="font-size: 0.8rem; color: #7f8c8d;">${cpuUsagePercent}%</div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4><i class="fas fa-hdd"></i> Storage Devices</h4>
                        ${storageHTML || '<p style="color: #7f8c8d;">No storage information available</p>'}
                    </div>

                    <div class="detail-section">
                        <h4><i class="fas fa-network-wired"></i> Network Interfaces</h4>
                        ${networkHTML || '<p style="color: #7f8c8d;">No network information available</p>'}
                    </div>
                </div>
            `;
        }

        // Utility functions
        function getPlatformIcon(platform) {
            switch(platform.toLowerCase()) {
                case 'windows': return 'windows';
                case 'linux': return 'linux';
                case 'darwin': return 'apple';
                default: return 'desktop';
            }
        }

        function updateTrends(data) {
            // Update trend indicators based on data
            const totalTrend = document.getElementById('total-trend');
            const activeTrend = document.getElementById('active-trend');
            const offlineTrend = document.getElementById('offline-trend');

            if (data.offline_clients > 0) {
                offlineTrend.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Needs attention';
                offlineTrend.className = 'stat-trend trend-down';
            } else {
                offlineTrend.innerHTML = '<i class="fas fa-check-circle"></i> All systems online';
                offlineTrend.className = 'stat-trend trend-up';
            }
        }

        function updateLastUpdateTime() {
            lastUpdateTime = new Date();
            const updateElement = document.getElementById('update-time');
            const alertElement = document.getElementById('last-update');

            updateElement.textContent = lastUpdateTime.toLocaleTimeString();
            alertElement.style.display = 'flex';
        }

        function showLoading() {
            const tbody = document.getElementById('clients-tbody');
            tbody.innerHTML = '<tr><td colspan="6" class="loading"><div class="spinner"></div></td></tr>';
        }

        function hideLoading() {
            // Loading is hidden when table is rendered
        }

        function showError(message) {
            const tbody = document.getElementById('clients-tbody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" style="text-align: center; padding: 40px;">
                        <div class="alert alert-warning" style="display: inline-flex;">
                            <i class="fas fa-exclamation-triangle"></i>
                            ${message}
                        </div>
                    </td>
                </tr>
            `;
        }

        function filterClients() {
            const searchTerm = document.getElementById('search-box').value.toLowerCase();
            const filteredClients = clientsData.filter(client =>
                client.client_id.toLowerCase().includes(searchTerm) ||
                client.computer_name.toLowerCase().includes(searchTerm) ||
                client.platform.toLowerCase().includes(searchTerm)
            );
            renderClientsTable(filteredClients);
        }

        function closeModal() {
            document.getElementById('client-modal').style.display = 'none';
        }

        function exportData() {
            const data = {
                timestamp: new Date().toISOString(),
                clients: clientsData,
                summary: {
                    total: clientsData.length,
                    active: clientsData.filter(c => c.status === 'active').length,
                    offline: clientsData.filter(c => c.status === 'offline').length
                }
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `system-monitor-export-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function exportClientData(clientId) {
            fetch(`/api/client/${clientId}`)
                .then(response => response.json())
                .then(data => {
                    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `client-${clientId}-${new Date().toISOString().split('T')[0]}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                })
                .catch(error => {
                    alert('Failed to export client data: ' + error.message);
                });
        }

        function pingClient(clientId) {
            // Simulate ping functionality
            const button = event.target.closest('button');
            const originalHTML = button.innerHTML;

            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            button.disabled = true;

            // Simulate network request
            setTimeout(() => {
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.style.background = '#27ae60';

                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.disabled = false;
                    button.style.background = '';
                }, 1000);
            }, 1500);
        }

        function showSettings() {
            alert('Settings panel coming soon! This will allow you to configure:\\n\\n• Auto-refresh intervals\\n• Alert thresholds\\n• Export formats\\n• User preferences');
        }

        // Event listeners
        window.onclick = function(event) {
            const modal = document.getElementById('client-modal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            } else if (event.ctrlKey && event.key === 'r') {
                event.preventDefault();
                loadData();
            }
        });

        // Initialize
        loadData();

        // Auto-refresh every 30 seconds
        setInterval(loadData, 30000);

        // Update time display every second
        setInterval(() => {
            if (lastUpdateTime) {
                const now = new Date();
                const diff = Math.floor((now - lastUpdateTime) / 1000);
                const updateElement = document.getElementById('update-time');

                if (diff < 60) {
                    updateElement.textContent = `${diff} seconds ago`;
                } else if (diff < 3600) {
                    updateElement.textContent = `${Math.floor(diff / 60)} minutes ago`;
                } else {
                    updateElement.textContent = lastUpdateTime.toLocaleTimeString();
                }
            }
        }, 1000);
    </script>
</body>
</html>
'''

@app.route('/')
def console():
    """Main console interface"""
    return render_template_string(CONSOLE_TEMPLATE)

if __name__ == '__main__':
    import socket

    init_database()

    # Get the local IP address
    try:
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
    except:
        local_ip = "127.0.0.1"

    print("=" * 60)
    print("🚀 System Monitor Central Server")
    print("=" * 60)
    print(f"📡 Server hostname: {hostname}")
    print(f"🌐 Server IP address: {local_ip}")
    print("=" * 60)
    print("🖥️  Web Console URLs:")
    print(f"   Local access:   http://localhost:5000")
    print(f"   Network access: http://{local_ip}:5000")
    print("=" * 60)
    print("📊 API Endpoints:")
    print("  POST /api/report - Receive client reports")
    print("  GET /api/clients - List all clients")
    print("  GET /api/client/<id> - Get client details")
    print("  GET /api/stats - Get statistics")
    print("=" * 60)
    print("🔧 Client Connection Commands:")
    print(f"   Local:   SystemMonitorClient.exe --server http://localhost:5000")
    print(f"   Remote:  SystemMonitorClient.exe --server http://{local_ip}:5000")
    print("=" * 60)
    print("🔥 Server is ready to accept connections from any network!")
    print("   Press Ctrl+C to stop")
    print("=" * 60)

    try:
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n" + "=" * 60)
        print("🛑 Server shutdown requested")
        print("=" * 60)
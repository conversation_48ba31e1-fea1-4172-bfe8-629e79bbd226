# 🚀 System Monitor - Standalone Client Deployment Guide

## 📋 Overview

The **SystemMonitorClient_Standalone.py** is a single, self-contained file that includes:
- ✅ Complete system information collection
- ✅ Automatic service installation and management
- ✅ Cross-platform support (Windows & Linux)
- ✅ No external dependencies (uses only Python standard library)
- ✅ Self-installing and self-configuring

## 🎯 Single File Deployment

### **What You Need:**
1. **One file only**: `SystemMonitorClient_Standalone.py`
2. **Python 3.6+** installed on target machines
3. **Administrator/Root privileges** for service installation

### **No Additional Files Required:**
- ❌ No requirements.txt
- ❌ No configuration files
- ❌ No installation scripts
- ❌ No external dependencies

## 🚀 Quick Deployment

### **Windows Deployment**

#### **Method 1: Direct Installation**
```cmd
# Copy the file to target machine
# Run as Administrator:
python SystemMonitorClient_Standalone.py --install --server http://your-server-ip:5000

# Optional: Custom settings
python SystemMonitorClient_Standalone.py --install --server http://*************:5000 --interval 30 --client-id "PC-001"
```

#### **Method 2: Test First, Then Install**
```cmd
# Test connection first
python SystemMonitorClient_Standalone.py --once --server http://your-server-ip:5000

# If successful, install as service
python SystemMonitorClient_Standalone.py --install --server http://your-server-ip:5000
```

### **Linux Deployment**

#### **Method 1: Direct Installation**
```bash
# Copy the file to target machine
# Run as root:
sudo python3 SystemMonitorClient_Standalone.py --install --server http://your-server-ip:5000

# Optional: Custom settings
sudo python3 SystemMonitorClient_Standalone.py --install --server http://*************:5000 --interval 30 --client-id "server-001"
```

#### **Method 2: Test First, Then Install**
```bash
# Test connection first
python3 SystemMonitorClient_Standalone.py --once --server http://your-server-ip:5000

# If successful, install as service
sudo python3 SystemMonitorClient_Standalone.py --install --server http://your-server-ip:5000
```

## 📖 Command Reference

### **Installation Commands**
```bash
# Basic installation
python SystemMonitorClient_Standalone.py --install --server http://server-ip:5000

# Custom installation
python SystemMonitorClient_Standalone.py --install \
    --server http://*************:5000 \
    --interval 30 \
    --client-id "custom-name"
```

### **Testing Commands**
```bash
# Single test run
python SystemMonitorClient_Standalone.py --once --server http://server-ip:5000

# Manual continuous monitoring (for testing)
python SystemMonitorClient_Standalone.py --server http://server-ip:5000 --interval 5
```

### **Management Commands**
```bash
# Uninstall service
python SystemMonitorClient_Standalone.py --uninstall

# Check service status (Windows)
sc query SystemMonitorClient

# Check service status (Linux)
sudo systemctl status systemmonitorclient
```

## 🔧 Configuration Options

| Parameter | Description | Default | Example |
|-----------|-------------|---------|---------|
| `--server` | Server URL | http://localhost:5000 | http://*************:5000 |
| `--interval` | Report interval (minutes) | 60 | 30 |
| `--client-id` | Custom client identifier | Computer hostname | "PC-001" |
| `--once` | Run once and exit | - | For testing |
| `--install` | Install as service | - | Requires admin/root |
| `--uninstall` | Remove service | - | Requires admin/root |

## 📁 Installation Details

### **Windows Installation**
- **Location**: `C:\Program Files\SystemMonitor\`
- **Service Name**: `SystemMonitorClient`
- **Files Created**:
  - `SystemMonitorClient.py` (copy of standalone file)
  - `config.json` (configuration)
  - `service_wrapper.py` (service wrapper)
  - `start_service.bat` (service startup)

### **Linux Installation**
- **Location**: `/opt/systemmonitor/`
- **Service Name**: `systemmonitorclient.service`
- **Files Created**:
  - `SystemMonitorClient.py` (copy of standalone file)
  - `config.json` (configuration)
  - `/etc/systemd/system/systemmonitorclient.service` (systemd service)

## 🌐 Mass Deployment

### **PowerShell Script (Windows Network)**
```powershell
# deploy_clients.ps1
$computers = @("PC1", "PC2", "PC3", "SERVER1")
$serverUrl = "http://*************:5000"
$clientFile = "SystemMonitorClient_Standalone.py"

foreach ($computer in $computers) {
    Write-Host "Deploying to $computer..."
    
    # Copy file
    Copy-Item $clientFile "\\$computer\C$\temp\"
    
    # Install remotely
    Invoke-Command -ComputerName $computer -ScriptBlock {
        cd C:\temp
        python SystemMonitorClient_Standalone.py --install --server $using:serverUrl --client-id $env:COMPUTERNAME
    }
}
```

### **Bash Script (Linux Network)**
```bash
#!/bin/bash
# deploy_clients.sh
SERVERS=("server1" "server2" "server3")
SERVER_URL="http://*************:5000"
CLIENT_FILE="SystemMonitorClient_Standalone.py"

for server in "${SERVERS[@]}"; do
    echo "Deploying to $server..."
    
    # Copy file
    scp $CLIENT_FILE root@$server:/tmp/
    
    # Install remotely
    ssh root@$server "cd /tmp && python3 SystemMonitorClient_Standalone.py --install --server $SERVER_URL --client-id $server"
done
```

## 🔍 Verification Steps

### **1. Check Installation**
```bash
# Windows
sc query SystemMonitorClient
dir "C:\Program Files\SystemMonitor"

# Linux
sudo systemctl status systemmonitorclient
ls -la /opt/systemmonitor/
```

### **2. Check Server Console**
- Open web console: `http://your-server:5000`
- Verify client appears in the list
- Check "Last Seen" timestamp
- Click "Details" to view system information

### **3. Check Logs**
```bash
# Windows
type "C:\Program Files\SystemMonitor\error.log"

# Linux
sudo journalctl -u systemmonitorclient -f
```

## 🚨 Troubleshooting

### **Common Issues**

#### **"Administrator privileges required"**
```bash
# Windows: Run Command Prompt as Administrator
# Linux: Use sudo
sudo python3 SystemMonitorClient_Standalone.py --install --server http://server:5000
```

#### **"Connection refused"**
```bash
# Test server connectivity
ping server-ip
telnet server-ip 5000

# Test with curl
curl http://server-ip:5000/api/stats
```

#### **Service won't start**
```bash
# Windows
sc start SystemMonitorClient
sc query SystemMonitorClient

# Linux
sudo systemctl start systemmonitorclient
sudo systemctl status systemmonitorclient
```

#### **Client not appearing in console**
1. Check server URL is correct
2. Verify network connectivity
3. Check firewall settings
4. Test with `--once` parameter first

### **Manual Cleanup**
```bash
# Windows
sc stop SystemMonitorClient
sc delete SystemMonitorClient
rmdir /s "C:\Program Files\SystemMonitor"

# Linux
sudo systemctl stop systemmonitorclient
sudo systemctl disable systemmonitorclient
sudo rm /etc/systemd/system/systemmonitorclient.service
sudo rm -rf /opt/systemmonitor
sudo systemctl daemon-reload
```

## 📊 Monitoring and Maintenance

### **Service Management**
```bash
# Windows
sc start SystemMonitorClient    # Start service
sc stop SystemMonitorClient     # Stop service
sc query SystemMonitorClient    # Check status

# Linux
sudo systemctl start systemmonitorclient     # Start service
sudo systemctl stop systemmonitorclient      # Stop service
sudo systemctl status systemmonitorclient    # Check status
sudo systemctl restart systemmonitorclient   # Restart service
```

### **Configuration Updates**
To change configuration:
1. Uninstall: `python SystemMonitorClient_Standalone.py --uninstall`
2. Reinstall with new settings: `python SystemMonitorClient_Standalone.py --install --server NEW_URL`

## 🎉 Benefits of Standalone Client

✅ **Single File Deployment**: Only one file to distribute  
✅ **No Dependencies**: Uses only Python standard library  
✅ **Self-Installing**: Handles service creation automatically  
✅ **Cross-Platform**: Works on Windows and Linux  
✅ **Self-Contained**: All functionality in one file  
✅ **Easy Distribution**: Email, USB, network copy  
✅ **Version Control**: Single file to update  
✅ **Minimal Footprint**: Small file size, low resource usage  

## 📞 Support

For issues:
1. Test with `--once` parameter first
2. Check network connectivity to server
3. Verify Python installation
4. Check administrator/root privileges
5. Review server logs for connection attempts

---

**System Monitor Standalone Client** - Enterprise monitoring made simple with single-file deployment!

@echo off
REM System Monitor Client Installer for Windows
REM This script installs and configures the system monitor client

setlocal enabledelayedexpansion

echo ========================================
echo System Monitor Client Installer
echo ========================================
echo.

REM Configuration
set INSTALL_DIR=C:\SystemMonitor
set SERVICE_NAME=SystemMonitorClient
set PYTHON_EXE=python
set DEFAULT_SERVER=http://localhost:5000
set DEFAULT_INTERVAL=60

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator... OK
) else (
    echo This script requires Administrator privileges.
    echo Please run as Administrator.
    pause
    exit /b 1
)

echo.
echo === Configuration ===
set /p SERVER_URL="Enter server URL [%DEFAULT_SERVER%]: "
if "!SERVER_URL!"=="" set SERVER_URL=%DEFAULT_SERVER%

set /p INTERVAL="Enter reporting interval in minutes [%DEFAULT_INTERVAL%]: "
if "!INTERVAL!"=="" set INTERVAL=%DEFAULT_INTERVAL%

set /p CLIENT_ID="Enter custom client ID (leave blank for hostname): "

echo.
echo Server URL: %SERVER_URL%
echo Interval: %INTERVAL% minutes
if not "!CLIENT_ID!"=="" echo Client ID: %CLIENT_ID%
echo.

set /p CONFIRM="Continue with installation? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo Installation cancelled.
    pause
    exit /b 0
)

echo.
echo === Installation Steps ===

REM Step 1: Create installation directory
echo [1/6] Creating installation directory...
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    echo Created directory: %INSTALL_DIR%
) else (
    echo Directory already exists: %INSTALL_DIR%
)

REM Step 2: Check Python installation
echo [2/6] Checking Python installation...
%PYTHON_EXE% --version >nul 2>&1
if %errorLevel% == 0 (
    %PYTHON_EXE% --version
    echo Python installation... OK
) else (
    echo Python not found! Please install Python 3.7+ and add to PATH.
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Step 3: Install Python dependencies
echo [3/6] Installing Python dependencies...
%PYTHON_EXE% -m pip install psutil requests flask pywin32
if %errorLevel% == 0 (
    echo Dependencies installed... OK
) else (
    echo Failed to install dependencies!
    pause
    exit /b 1
)

REM Step 4: Copy client files
echo [4/6] Copying client files...
copy "%~dp0system_monitor_client.py" "%INSTALL_DIR%\" >nul
if %errorLevel% == 0 (
    echo Client script copied... OK
) else (
    echo Failed to copy client script!
    pause
    exit /b 1
)

REM Step 5: Create service wrapper script
echo [5/6] Creating service wrapper...
set WRAPPER_SCRIPT=%INSTALL_DIR%\run_client.bat
echo @echo off > "%WRAPPER_SCRIPT%"
echo cd /d "%INSTALL_DIR%" >> "%WRAPPER_SCRIPT%"
if not "!CLIENT_ID!"=="" (
    echo %PYTHON_EXE% system_monitor_client.py --server %SERVER_URL% --interval %INTERVAL% --client-id "%CLIENT_ID%" >> "%WRAPPER_SCRIPT%"
) else (
    echo %PYTHON_EXE% system_monitor_client.py --server %SERVER_URL% --interval %INTERVAL% >> "%WRAPPER_SCRIPT%"
)
echo Service wrapper created... OK

REM Step 6: Create and start Windows service
echo [6/6] Installing Windows service...

REM Create service installer script
set SERVICE_INSTALLER=%INSTALL_DIR%\install_service.py
echo import win32serviceutil > "%SERVICE_INSTALLER%"
echo import win32service >> "%SERVICE_INSTALLER%"
echo import win32event >> "%SERVICE_INSTALLER%"
echo import servicemanager >> "%SERVICE_INSTALLER%"
echo import subprocess >> "%SERVICE_INSTALLER%"
echo import os >> "%SERVICE_INSTALLER%"
echo import time >> "%SERVICE_INSTALLER%"
echo. >> "%SERVICE_INSTALLER%"
echo class SystemMonitorService(win32serviceutil.ServiceFramework): >> "%SERVICE_INSTALLER%"
echo     _svc_name_ = "%SERVICE_NAME%" >> "%SERVICE_INSTALLER%"
echo     _svc_display_name_ = "System Monitor Client" >> "%SERVICE_INSTALLER%"
echo     _svc_description_ = "Monitors system specifications and reports to central server" >> "%SERVICE_INSTALLER%"
echo. >> "%SERVICE_INSTALLER%"
echo     def __init__(self, args): >> "%SERVICE_INSTALLER%"
echo         win32serviceutil.ServiceFramework.__init__(self, args) >> "%SERVICE_INSTALLER%"
echo         self.hWaitStop = win32event.CreateEvent(None, 0, 0, None) >> "%SERVICE_INSTALLER%"
echo         self.running = True >> "%SERVICE_INSTALLER%"
echo. >> "%SERVICE_INSTALLER%"
echo     def SvcStop(self): >> "%SERVICE_INSTALLER%"
echo         self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING) >> "%SERVICE_INSTALLER%"
echo         self.running = False >> "%SERVICE_INSTALLER%"
echo         win32event.SetEvent(self.hWaitStop) >> "%SERVICE_INSTALLER%"
echo. >> "%SERVICE_INSTALLER%"
echo     def SvcDoRun(self): >> "%SERVICE_INSTALLER%"
echo         servicemanager.LogMsg(servicemanager.EVENTLOG_INFORMATION_TYPE, >> "%SERVICE_INSTALLER%"
echo                               servicemanager.PYS_SERVICE_STARTED, >> "%SERVICE_INSTALLER%"
echo                               (self._svc_name_, '')) >> "%SERVICE_INSTALLER%"
echo         self.main() >> "%SERVICE_INSTALLER%"
echo. >> "%SERVICE_INSTALLER%"
echo     def main(self): >> "%SERVICE_INSTALLER%"
echo         while self.running: >> "%SERVICE_INSTALLER%"
echo             try: >> "%SERVICE_INSTALLER%"
echo                 script_path = r'%WRAPPER_SCRIPT%' >> "%SERVICE_INSTALLER%"
echo                 subprocess.run(script_path, shell=True, check=True) >> "%SERVICE_INSTALLER%"
echo             except Exception as e: >> "%SERVICE_INSTALLER%"
echo                 servicemanager.LogErrorMsg(f'Service error: {e}') >> "%SERVICE_INSTALLER%"
echo             time.sleep(300)  # Wait 5 minutes before retry >> "%SERVICE_INSTALLER%"
echo. >> "%SERVICE_INSTALLER%"
echo if __name__ == '__main__': >> "%SERVICE_INSTALLER%"
echo     win32serviceutil.HandleCommandLine(SystemMonitorService) >> "%SERVICE_INSTALLER%"

REM Install the service
cd /d "%INSTALL_DIR%"
%PYTHON_EXE% install_service.py install
if %errorLevel% == 0 (
    echo Service installed... OK
    
    REM Start the service
    echo Starting service...
    %PYTHON_EXE% install_service.py start
    if %errorLevel__ == 0 (
        echo Service started... OK
    ) else (
        echo Warning: Service installed but failed to start
        echo You can start it manually from Services.msc
    )
) else (
    echo Failed to install service!
    echo You can run the client manually using: %WRAPPER_SCRIPT%
)

echo.
echo === Installation Complete ===
echo.
echo Installation directory: %INSTALL_DIR%
echo Service name: %SERVICE_NAME%
echo Server URL: %SERVER_URL%
echo Reporting interval: %INTERVAL% minutes
if not "!CLIENT_ID!"=="" echo Client ID: %CLIENT_ID%
echo.
echo Management commands:
echo   Start service:   sc start %SERVICE_NAME%
echo   Stop service:    sc stop %SERVICE_NAME%
echo   Remove service:  %PYTHON_EXE% "%INSTALL_DIR%\install_service.py" remove
echo   Manual run:      "%WRAPPER_SCRIPT%"
echo.
echo Check service status in Services.msc or Event Viewer
echo.

REM Test connection
echo === Testing Connection ===
echo Testing connection to server...
cd /d "%INSTALL_DIR%"
if not "!CLIENT_ID!"=="" (
    %PYTHON_EXE% system_monitor_client.py --server %SERVER_URL% --client-id "%CLIENT_ID%" --once
) else (
    %PYTHON_EXE% system_monitor_client.py --server %SERVER_URL% --once
)

if %errorLevel% == 0 (
    echo.
    echo Connection test... OK
    echo The client is now installed and reporting to the server.
) else (
    echo.
    echo Connection test... FAILED
    echo Please check server URL and network connectivity.
    echo Service will continue trying to connect automatically.
)

echo.
pause
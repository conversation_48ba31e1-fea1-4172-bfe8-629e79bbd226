#!/bin/bash
# System Monitor - Production Startup Script
# This script starts both the server and client for testing

echo "========================================"
echo "System Monitor - Production Startup"
echo "========================================"
echo

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv .venv
    echo "Installing dependencies..."
    .venv/bin/pip install psutil requests flask
fi

echo "Starting System Monitor Server..."
gnome-terminal --title="System Monitor Server" -- bash -c '.venv/bin/python "System Monitor Central Server.py"; exec bash' &

echo "Waiting for server to start..."
sleep 5

echo "Starting System Monitor Client (continuous monitoring)..."
gnome-terminal --title="System Monitor Client" -- bash -c '.venv/bin/python "System Monitor Client.py" --interval 5; exec bash' &

echo
echo "========================================"
echo "System Monitor Started Successfully!"
echo "========================================"
echo
echo "Server Console: http://localhost:5000"
echo
echo "To stop the system:"
echo "1. Close both terminal windows"
echo "2. Or press Ctrl+C in each window"
echo

#!/bin/bash
# System Monitor Client Installer for Linux
# This script installs and configures the system monitor client

set -e

# Configuration
INSTALL_DIR="/opt/system-monitor"
SERVICE_NAME="system-monitor-client"
PYTHON_EXE="python3"
DEFAULT_SERVER="http://localhost:5000"
DEFAULT_INTERVAL="60"
USER_NAME="monitor"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "========================================"
echo "System Monitor Client Installer (Linux)"
echo "========================================"
echo

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}This script requires root privileges.${NC}"
    echo "Please run with sudo: sudo $0"
    exit 1
fi

echo -e "${GREEN}Running as root... OK${NC}"

# Get configuration from user
echo
echo "=== Configuration ==="
read -p "Enter server URL [$DEFAULT_SERVER]: " SERVER_URL
SERVER_URL=${SERVER_URL:-$DEFAULT_SERVER}

read -p "Enter reporting interval in minutes [$DEFAULT_INTERVAL]: " INTERVAL
INTERVAL=${INTERVAL:-$DEFAULT_INTERVAL}

read -p "Enter custom client ID (leave blank for hostname): " CLIENT_ID

echo
echo "Server URL: $SERVER_URL"
echo "Interval: $INTERVAL minutes"
if [ -n "$CLIENT_ID" ]; then
    echo "Client ID: $CLIENT_ID"
fi
echo

read -p "Continue with installation? (y/N): " -n 1 -r CONFIRM
echo
if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
    echo "Installation cancelled."
    exit 0
fi

echo
echo "=== Installation Steps ==="

# Step 1: Install system dependencies
echo "[1/8] Installing system dependencies..."
if command -v apt-get > /dev/null; then
    # Debian/Ubuntu
    apt-get update
    apt-get install -y python3 python3-pip python3-venv
elif command -v yum > /dev/null; then
    # RHEL/CentOS
    yum install -y python3 python3-pip
elif command -v dnf > /dev/null; then
    # Fedora
    dnf install -y python3 python3-pip
elif command -v pacman > /dev/null; then
    # Arch Linux
    pacman -S --noconfirm python python-pip
else
    echo -e "${YELLOW}Warning: Unknown package manager. Please ensure Python 3 and pip are installed.${NC}"
fi
echo -e "${GREEN}System dependencies... OK${NC}"

# Step 2: Create user account
echo "[2/8] Creating service user..."
if ! id "$USER_NAME" &>/dev/null; then
    useradd -r -s /bin/false -d $INSTALL_DIR $USER_NAME
    echo -e "${GREEN}User '$USER_NAME' created... OK${NC}"
else
    echo -e "${YELLOW}User '$USER_NAME' already exists... OK${NC}"
fi

# Step 3: Create installation directory
echo "[3/8] Creating installation directory..."
mkdir -p $INSTALL_DIR
chown $USER_NAME:$USER_NAME $INSTALL_DIR
chmod 755 $INSTALL_DIR
echo -e "${GREEN}Directory created: $INSTALL_DIR${NC}"

# Step 4: Copy client files
echo "[4/8] Copying client files..."
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cp "$SCRIPT_DIR/System Monitor Client.py" "$INSTALL_DIR/"
chown $USER_NAME:$USER_NAME "$INSTALL_DIR/System Monitor Client.py"
chmod 755 "$INSTALL_DIR/System Monitor Client.py"
echo -e "${GREEN}Client script copied... OK${NC}"

# Step 5: Create virtual environment and install dependencies
echo "[5/8] Setting up Python environment..."
cd $INSTALL_DIR
sudo -u $USER_NAME python3 -m venv venv
sudo -u $USER_NAME ./venv/bin/pip install --upgrade pip
sudo -u $USER_NAME ./venv/bin/pip install psutil requests flask
echo -e "${GREEN}Python environment setup... OK${NC}"

# Step 6: Create configuration file
echo "[6/8] Creating configuration file..."
cat > "$INSTALL_DIR/config.conf" << EOF
SERVER_URL=$SERVER_URL
INTERVAL=$INTERVAL
CLIENT_ID=$CLIENT_ID
EOF
chown $USER_NAME:$USER_NAME "$INSTALL_DIR/config.conf"
chmod 600 "$INSTALL_DIR/config.conf"
echo -e "${GREEN}Configuration file created... OK${NC}"

# Step 7: Create startup script
echo "[7/8] Creating startup script..."
cat > "$INSTALL_DIR/run_client.sh" << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
source ./venv/bin/activate
source ./config.conf

ARGS="--server $SERVER_URL --interval $INTERVAL"
if [ -n "$CLIENT_ID" ]; then
    ARGS="$ARGS --client-id $CLIENT_ID"
fi

exec python "System Monitor Client.py" $ARGS
EOF

chmod +x "$INSTALL_DIR/run_client.sh"
chown $USER_NAME:$USER_NAME "$INSTALL_DIR/run_client.sh"
echo -e "${GREEN}Startup script created... OK${NC}"

# Step 8: Create and install systemd service
echo "[8/8] Installing systemd service..."
cat > "/etc/systemd/system/$SERVICE_NAME.service" << EOF
[Unit]
Description=System Monitor Client
After=network.target network-online.target
Wants=network-online.target

[Service]
Type=simple
User=$USER_NAME
Group=$USER_NAME
WorkingDirectory=$INSTALL_DIR
ExecStart=$INSTALL_DIR/run_client.sh
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$INSTALL_DIR

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and enable service
systemctl daemon-reload
systemctl enable $SERVICE_NAME
echo -e "${GREEN}Service installed... OK${NC}"

echo
echo "=== Installation Complete ==="
echo
echo "Installation directory: $INSTALL_DIR"
echo "Service name: $SERVICE_NAME"
echo "Server URL: $SERVER_URL"
echo "Reporting interval: $INTERVAL minutes"
if [ -n "$CLIENT_ID" ]; then
    echo "Client ID: $CLIENT_ID"
fi
echo
echo "Management commands:"
echo "  Start service:   systemctl start $SERVICE_NAME"
echo "  Stop service:    systemctl stop $SERVICE_NAME"
echo "  Restart service: systemctl restart $SERVICE_NAME"
echo "  Check status:    systemctl status $SERVICE_NAME"
echo "  View logs:       journalctl -u $SERVICE_NAME -f"
echo "  Manual run:      sudo -u $USER_NAME $INSTALL_DIR/run_client.sh"
echo

# Test connection and start service
echo "=== Testing Connection ==="
echo "Testing connection to server..."
cd $INSTALL_DIR
sudo -u $USER_NAME ./venv/bin/python "System Monitor Client.py" --server "$SERVER_URL" --once $([ -n "$CLIENT_ID" ] && echo "--client-id '$CLIENT_ID'")

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Connection test... OK${NC}"
    echo "Starting service..."
    systemctl start $SERVICE_NAME
    sleep 3
    if systemctl is-active --quiet $SERVICE_NAME; then
        echo -e "${GREEN}Service started successfully!${NC}"
        echo "The client is now installed and reporting to the server."
    else
        echo -e "${RED}Service failed to start. Check logs with: journalctl -u $SERVICE_NAME${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}Connection test failed.${NC}"
    echo "Please check server URL and network connectivity."
    echo "You can start the service manually later with: systemctl start $SERVICE_NAME"
    echo "The service will continue trying to connect automatically."
fi

echo
echo "Installation completed successfully!"

# Create uninstaller script
cat > "$INSTALL_DIR/uninstall.sh" << EOF
#!/bin/bash
echo "Uninstalling System Monitor Client..."

# Stop and disable service
systemctl stop $SERVICE_NAME 2>/dev/null || true
systemctl disable $SERVICE_NAME 2>/dev/null || true
rm -f /etc/systemd/system/$SERVICE_NAME.service
systemctl daemon-reload

# Remove installation directory
rm -rf $INSTALL_DIR

# Remove user
userdel $USER_NAME 2>/dev/null || true

echo "Uninstallation complete."
EOF
chmod +x "$INSTALL_DIR/uninstall.sh"

echo "To uninstall, run: $INSTALL_DIR/uninstall.sh"
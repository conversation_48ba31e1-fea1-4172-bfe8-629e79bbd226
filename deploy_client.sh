#!/bin/bash
# System Monitor Client - Easy Deployment Script
# This script simplifies the deployment of the standalone client

echo "========================================"
echo "System Monitor Client - Easy Deployment"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed"
    echo "Please install Python 3.6+ and try again"
    echo "Ubuntu/Debian: sudo apt install python3"
    echo "CentOS/RHEL: sudo yum install python3"
    exit 1
fi

echo "✅ Python 3 is installed"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Root privileges required"
    echo "Please run this script with sudo"
    exit 1
fi

echo "✅ Running with root privileges"

# Get server URL from user
read -p "Enter server URL (e.g., http://*************:5000): " SERVER_URL
if [ -z "$SERVER_URL" ]; then
    echo "❌ Server URL is required"
    exit 1
fi

# Get optional client ID
read -p "Enter custom client ID (or press Enter for default): " CLIENT_ID

# Get optional interval
read -p "Enter reporting interval in minutes (or press Enter for 60): " INTERVAL
if [ -z "$INTERVAL" ]; then
    INTERVAL=60
fi

echo
echo "🔧 Installation Settings:"
echo "   Server URL: $SERVER_URL"
echo "   Client ID: $CLIENT_ID"
echo "   Interval: $INTERVAL minutes"
echo

read -p "Proceed with installation? (y/N): " CONFIRM
if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
    echo "Installation cancelled"
    exit 0
fi

echo
echo "🚀 Installing System Monitor Client..."

# Build installation command
INSTALL_CMD="python3 SystemMonitorClient_Standalone.py --install --server $SERVER_URL --interval $INTERVAL"
if [ ! -z "$CLIENT_ID" ]; then
    INSTALL_CMD="$INSTALL_CMD --client-id \"$CLIENT_ID\""
fi

# Run installation
eval $INSTALL_CMD

if [ $? -ne 0 ]; then
    echo
    echo "❌ Installation failed"
    echo "Please check the error messages above"
    exit 1
fi

echo
echo "✅ Installation completed successfully!"
echo
echo "📊 You can now check the server console at: $SERVER_URL"
echo "🔧 Service name: systemmonitorclient"
echo "📁 Installation directory: /opt/systemmonitor"
echo
echo "Service management commands:"
echo "  sudo systemctl status systemmonitorclient    # Check status"
echo "  sudo systemctl restart systemmonitorclient   # Restart service"
echo "  sudo systemctl stop systemmonitorclient      # Stop service"
echo
echo "To uninstall later, run:"
echo "python3 SystemMonitorClient_Standalone.py --uninstall"
echo

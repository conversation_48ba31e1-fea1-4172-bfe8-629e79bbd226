@echo off
REM System Monitor Client - Easy Deployment Script
REM This script simplifies the deployment of the standalone client

echo ========================================
echo System Monitor Client - Easy Deployment
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.6+ and try again
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python is installed

REM Check if running as Administrator
net session >nul 2>&1
if errorlevel 1 (
    echo ❌ Administrator privileges required
    echo Please run this script as Administrator
    pause
    exit /b 1
)

echo ✅ Running with Administrator privileges

REM Get server URL from user
set /p SERVER_URL="Enter server URL (e.g., http://*************:5000): "
if "%SERVER_URL%"=="" (
    echo ❌ Server URL is required
    pause
    exit /b 1
)

REM Get optional client ID
set /p CLIENT_ID="Enter custom client ID (or press Enter for default): "

REM Get optional interval
set /p INTERVAL="Enter reporting interval in minutes (or press Enter for 60): "
if "%INTERVAL%"=="" set INTERVAL=60

echo.
echo 🔧 Installation Settings:
echo    Server URL: %SERVER_URL%
echo    Client ID: %CLIENT_ID%
echo    Interval: %INTERVAL% minutes
echo.

set /p CONFIRM="Proceed with installation? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo Installation cancelled
    pause
    exit /b 0
)

echo.
echo 🚀 Installing System Monitor Client...

REM Build installation command
set INSTALL_CMD=python SystemMonitorClient_Standalone.py --install --server %SERVER_URL% --interval %INTERVAL%
if not "%CLIENT_ID%"=="" set INSTALL_CMD=%INSTALL_CMD% --client-id "%CLIENT_ID%"

REM Run installation
%INSTALL_CMD%

if errorlevel 1 (
    echo.
    echo ❌ Installation failed
    echo Please check the error messages above
    pause
    exit /b 1
)

echo.
echo ✅ Installation completed successfully!
echo.
echo 📊 You can now check the server console at: %SERVER_URL%
echo 🔧 Service name: SystemMonitorClient
echo 📁 Installation directory: C:\Program Files\SystemMonitor
echo.
echo To uninstall later, run:
echo python SystemMonitorClient_Standalone.py --uninstall
echo.
pause

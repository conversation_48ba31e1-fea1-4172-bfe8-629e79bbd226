#!/usr/bin/env python3

"""
System Monitor Client - Collects system specifications and reports to central server
Similar to SCCM client functionality

"""

import json
import platform
import psutil
import socket
import subprocess
import time
import requests
from datetime import datetime
import os
import sys

class SystemMonitorClient:
    def __init__(self, server_url="http://localhost:5000", client_id=None):
        self.server_url = server_url.rstrip('/')
        self.client_id = client_id or socket.gethostname()
        self.system_info = {}
        
    def get_windows_version(self):
        """Get detailed Windows version information"""
        try:
            if platform.system() == "Windows":
                import winreg
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                   r"SOFTWARE\Microsoft\Windows NT\CurrentVersion")
                
                product_name = winreg.QueryValueEx(key, "ProductName")[0]
                build_number = winreg.QueryValueEx(key, "CurrentBuild")[0]
                display_version = winreg.QueryValueEx(key, "DisplayVersion")[0]
                winreg.CloseKey(key)
                
                return f"{product_name} (Build {build_number}, Version {display_version})"
            else:
                return f"{platform.system()} {platform.release()}"
        except Exception as e:
            return f"{platform.system()} {platform.release()}"
    
    def get_system_specifications(self):
        """Collect comprehensive system specifications"""
        try:
            # Basic system information
            self.system_info = {
                "client_id": self.client_id,
                "computer_name": socket.gethostname(),
                "timestamp": datetime.now().isoformat(),
                "platform": platform.system(),
                "platform_version": self.get_windows_version(),
                "architecture": platform.architecture()[0],
                "processor": platform.processor(),
                "machine": platform.machine(),
            }
            
            # Memory information
            memory = psutil.virtual_memory()
            self.system_info["memory"] = {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "percentage": memory.percent
            }
            
            # Storage information
            storage_devices = []
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    storage_devices.append({
                        "device": partition.device,
                        "mountpoint": partition.mountpoint,
                        "filesystem": partition.fstype,
                        "total_gb": round(usage.total / (1024**3), 2),
                        "used_gb": round(usage.used / (1024**3), 2),
                        "free_gb": round(usage.free / (1024**3), 2),
                        "percentage": round((usage.used / usage.total) * 100, 2)
                    })
                except PermissionError:
                    continue
            
            self.system_info["storage"] = storage_devices
            
            # Network information
            network_interfaces = []
            for interface_name, interface_addresses in psutil.net_if_addrs().items():
                for address in interface_addresses:
                    if address.family == socket.AF_INET:
                        network_interfaces.append({
                            "interface": interface_name,
                            "ip_address": address.address,
                            "netmask": address.netmask
                        })
            
            self.system_info["network"] = network_interfaces
            
            # System uptime
            boot_time = psutil.boot_time()
            uptime_seconds = time.time() - boot_time
            uptime_days = int(uptime_seconds // 86400)
            uptime_hours = int((uptime_seconds % 86400) // 3600)
            uptime_minutes = int((uptime_seconds % 3600) // 60)
            
            self.system_info["uptime"] = {
                "boot_time": datetime.fromtimestamp(boot_time).isoformat(),
                "uptime_days": uptime_days,
                "uptime_hours": uptime_hours,
                "uptime_minutes": uptime_minutes,
                "uptime_formatted": f"{uptime_days}d {uptime_hours}h {uptime_minutes}m"
            }
            
            # CPU information
            self.system_info["cpu"] = {
                "physical_cores": psutil.cpu_count(logical=False),
                "logical_cores": psutil.cpu_count(logical=True),
                "current_frequency": psutil.cpu_freq().current if psutil.cpu_freq() else None,
                "usage_percent": psutil.cpu_percent(interval=1)
            }
            
            return self.system_info
            
        except Exception as e:
            print(f"Error collecting system information: {e}")
            return None
    
    def report_to_server(self):
        """Send system information to central server"""
        try:
            response = requests.post(
                f"{self.server_url}/api/report",
                json=self.system_info,
                timeout=30,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            print(f"Successfully reported to server: {response.json()}")
            return True
        except requests.exceptions.RequestException as e:
            print(f"Failed to report to server: {e}")
            return False
    
    def save_local_report(self, filename=None):
        """Save system information to local file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"system_report_{self.client_id}_{timestamp}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(self.system_info, f, indent=2)
            print(f"System report saved to: {filename}")
            return filename
        except Exception as e:
            print(f"Failed to save report: {e}")
            return None
    
    def run_continuous_monitoring(self, interval_minutes=60):
        """Run continuous monitoring with specified interval"""
        print(f"Starting continuous monitoring (interval: {interval_minutes} minutes)")
        print(f"Reporting to server: {self.server_url}")
        print(f"Client ID: {self.client_id}")
        
        while True:
            try:
                print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Collecting system information...")
                
                if self.get_system_specifications():
                    # Try to report to server
                    if not self.report_to_server():
                        # If server reporting fails, save locally
                        print("Server reporting failed, saving locally...")
                        self.save_local_report()
                
                print(f"Next update in {interval_minutes} minutes...")
                time.sleep(interval_minutes * 60)
                
            except KeyboardInterrupt:
                print("\nMonitoring stopped by user")
                break
            except Exception as e:
                print(f"Error in monitoring loop: {e}")
                time.sleep(60)  # Wait 1 minute before retrying

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="System Monitor Client")
    parser.add_argument("--server", default="http://localhost:5000", 
                       help="Server URL (default: http://localhost:5000)")
    parser.add_argument("--client-id", help="Custom client ID")
    parser.add_argument("--interval", type=int, default=60, 
                       help="Monitoring interval in minutes (default: 60)")
    parser.add_argument("--once", action="store_true", 
                       help="Run once and exit")
    parser.add_argument("--local-only", action="store_true", 
                       help="Save report locally only")
    
    args = parser.parse_args()
    
    client = SystemMonitorClient(args.server, args.client_id)
    
    if args.once:
        print("Running single system specification collection...")
        if client.get_system_specifications():
            if args.local_only:
                client.save_local_report()
            else:
                if not client.report_to_server():
                    client.save_local_report()
        else:
            print("Failed to collect system information")
            sys.exit(1)
    else:
        client.run_continuous_monitoring(args.interval)

if __name__ == "__main__":
    main()
#!/usr/bin/env python3

import platform
import subprocess

def test_bios_collection():
    """Test BIOS information collection"""
    print("Testing BIOS information collection...")
    print("=" * 50)
    
    bios_info = {
        "version": "Unknown",
        "date": "Unknown",
        "vendor": "Unknown",
        "serial_number": "Unknown"
    }
    
    try:
        if platform.system() == "Windows":
            print("Platform: Windows - Testing WMI...")
            # Use WMI to get BIOS information on Windows
            try:
                import wmi
                print("WMI module imported successfully")
                c = wmi.WMI()
                print("WMI connection established")
                bios = c.Win32_BIOS()[0]
                print("BIOS object retrieved")
                
                bios_info["version"] = bios.SMBIOSBIOSVersion or "Unknown"
                bios_info["date"] = bios.ReleaseDate[:8] if bios.ReleaseDate else "Unknown"
                bios_info["vendor"] = bios.Manufacturer or "Unknown"
                bios_info["serial_number"] = bios.SerialNumber or "Unknown"
                
                print(f"Raw BIOS data:")
                print(f"  SMBIOSBIOSVersion: {bios.SMBIOSBIOSVersion}")
                print(f"  ReleaseDate: {bios.ReleaseDate}")
                print(f"  Manufacturer: {bios.Manufacturer}")
                print(f"  SerialNumber: {bios.SerialNumber}")
                
                # Format the date to be more readable
                if bios_info["date"] != "Unknown" and len(bios_info["date"]) >= 8:
                    try:
                        date_str = bios_info["date"]
                        formatted_date = f"{date_str[6:8]}/{date_str[4:6]}/{date_str[0:4]}"
                        bios_info["date"] = formatted_date
                        print(f"  Formatted date: {formatted_date}")
                    except Exception as e:
                        print(f"  Date formatting error: {e}")
                        
            except ImportError as e:
                print(f"WMI import failed: {e}")
                print("Trying registry fallback...")
                # Fallback to registry if WMI is not available
                try:
                    import winreg
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                       r"HARDWARE\DESCRIPTION\System\BIOS")
                    try:
                        bios_info["version"] = winreg.QueryValueEx(key, "BIOSVersion")[0]
                        print(f"Registry BIOSVersion: {bios_info['version']}")
                    except Exception as e:
                        print(f"Registry BIOSVersion error: {e}")
                    try:
                        bios_info["date"] = winreg.QueryValueEx(key, "BIOSReleaseDate")[0]
                        print(f"Registry BIOSReleaseDate: {bios_info['date']}")
                    except Exception as e:
                        print(f"Registry BIOSReleaseDate error: {e}")
                    try:
                        bios_info["vendor"] = winreg.QueryValueEx(key, "BIOSVendor")[0]
                        print(f"Registry BIOSVendor: {bios_info['vendor']}")
                    except Exception as e:
                        print(f"Registry BIOSVendor error: {e}")
                    winreg.CloseKey(key)
                except Exception as e:
                    print(f"Registry access failed: {e}")
            except Exception as e:
                print(f"WMI error: {e}")
                        
        else:
            print("Platform: Non-Windows - Testing dmidecode...")
            # Linux/Unix systems - use dmidecode if available
            try:
                # Try dmidecode for BIOS information
                result = subprocess.run(['dmidecode', '-t', 'bios'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        line = line.strip()
                        if line.startswith('Version:'):
                            bios_info["version"] = line.split(':', 1)[1].strip()
                        elif line.startswith('Release Date:'):
                            bios_info["date"] = line.split(':', 1)[1].strip()
                        elif line.startswith('Vendor:'):
                            bios_info["vendor"] = line.split(':', 1)[1].strip()
                else:
                    print(f"dmidecode failed with return code: {result.returncode}")
                    print(f"stderr: {result.stderr}")
            except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError) as e:
                print(f"dmidecode failed: {e}")
                # Try alternative methods for Linux
                try:
                    # Try reading from /sys/class/dmi/id/
                    with open('/sys/class/dmi/id/bios_version', 'r') as f:
                        bios_info["version"] = f.read().strip()
                        print(f"DMI bios_version: {bios_info['version']}")
                except Exception as e:
                    print(f"DMI bios_version error: {e}")
                try:
                    with open('/sys/class/dmi/id/bios_date', 'r') as f:
                        bios_info["date"] = f.read().strip()
                        print(f"DMI bios_date: {bios_info['date']}")
                except Exception as e:
                    print(f"DMI bios_date error: {e}")
                try:
                    with open('/sys/class/dmi/id/bios_vendor', 'r') as f:
                        bios_info["vendor"] = f.read().strip()
                        print(f"DMI bios_vendor: {bios_info['vendor']}")
                except Exception as e:
                    print(f"DMI bios_vendor error: {e}")
                        
    except Exception as e:
        print(f"General error: {e}")
        
    print("=" * 50)
    print("Final BIOS Information:")
    for key, value in bios_info.items():
        print(f"  {key}: {value}")
    print("=" * 50)
    
    return bios_info

if __name__ == "__main__":
    test_bios_collection()
